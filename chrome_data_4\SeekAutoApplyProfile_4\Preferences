{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_info": [{"access_point": 31, "account_id": "106558212763703608723", "accountcapabilities": {"accountcapabilities/g42tslldmfya": 1, "accountcapabilities/g44tilldmfya": 0, "accountcapabilities/ge2dinbnmnqxa": 0, "accountcapabilities/ge2tkmznmnqxa": 1, "accountcapabilities/ge2tknznmnqxa": 1, "accountcapabilities/ge2tkobnmnqxa": 1, "accountcapabilities/ge3dgmjnmnqxa": 1, "accountcapabilities/ge3dgobnmnqxa": 1, "accountcapabilities/geydgnznmnqxa": 1, "accountcapabilities/geytcnbnmnqxa": 1, "accountcapabilities/gezdcnbnmnqxa": 1, "accountcapabilities/gezdsmbnmnqxa": 0, "accountcapabilities/geztenjnmnqxa": 1, "accountcapabilities/gi2tklldmfya": 1, "accountcapabilities/gu2dqlldmfya": 1, "accountcapabilities/gu4dmlldmfya": 0, "accountcapabilities/guydolldmfya": 0, "accountcapabilities/guzdslldmfya": 0, "accountcapabilities/haytqlldmfya": 1, "accountcapabilities/he4tolldmfya": 0}, "email": "<EMAIL>", "full_name": "<PERSON>", "gaia": "106558212763703608723", "given_name": "<PERSON>", "hd": "NO_HOSTED_DOMAIN", "is_supervised_child": 0, "is_under_advanced_protection": false, "last_downloaded_image_url_with_size": "https://lh3.googleusercontent.com/a/ACg8ocLRC5fk4LYLEmsjhMcuqZDPVfjXq1kFTNgjXMVqosofjw-juQ=s256-c-ns", "locale": "en-GB", "picture_url": "https://lh3.googleusercontent.com/a/ACg8ocLRC5fk4LYLEmsjhMcuqZDPVfjXq1kFTNgjXMVqosofjw-juQ=s96-c"}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1293, "left": 776, "maximized": false, "right": 2696, "top": 213, "work_area_bottom": 1040, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 20058, "default_apps_install_state": 3, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "f42bc87c-1cdc-4bad-a56a-86c3ee0ba087", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "cws_info_timestamp": "*****************"}, "gaia_cookie": {"changed_time": **********.4, "hash": "ANJL2uC8dLwJfg4ERyE9Xe9Sblw=", "last_list_accounts_data": "[\"gaia.l.a.r\",[[\"gaia.l.a\",1,\"<PERSON>\",\"<EMAIL>\",\"https://lh3.googleusercontent.com/-GlYENnCIVZ0/AAAAAAAAAAI/AAAAAAAAAAA/oW9pzbb6h9A/s48-c/photo.jpg\",1,1,0,null,1,\"106558212763703608723\",null,null,null,null,1]]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin": {"REFRESH_TOKEN_RECEIVED": {"time": "2025-07-07T06:41:23.489Z", "value": "Successful (106558212763703608723)"}}, "signin_scoped_device_id": "e5b26b10-7524-43af-9d1d-c7e02700afd0"}}, "https_upgrade_navigations": {"2025-07-07": 530, "2025-07-08": 70}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "13396278195664480", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13396278195663953", "recent_session_start_times": ["13396401342202440", "13396344060907802", "13396278195663953"], "session_last_active_time": "13396401347203275", "session_start_time": "13396401342202440"}, "intl": {"selected_languages": "en-GB,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"en": 95}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "85bTtY4C655iHZFbaKBaxM5g5R4vgJpWUl8RbEzvPwMLz992iMSOE13Y3tJZtW4DJPR3IqC89YzdGVNOq72+nA=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 9}, "optimization_guide": {"predictionmodelfetcher": {"last_fetch_attempt": "13396406681234192", "last_fetch_success": "13396406681502188"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "DIGITAL_CREDENTIALS_LOW_FRICTION": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://www.seek.co.nz:443,*": {"last_modified": "*****************", "setting": {"https://www.seek.co.nz/": {"couldShowBannerEvents": 1.3396344063351444e+16, "next_install_text_animation": {"delay": "***********", "last_shown": "*****************"}}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.co.nz:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]google.co.nz,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]seek.co.nz,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]seek.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": true}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://accounts.google.co.nz:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://accounts.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://login.seek.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.seek.co.nz:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 4}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3396406671390612e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 13.5, "rawScore": 26.***************}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3396344078706264e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.1, "rawScore": 2.1}}, "https://www.seek.co.nz:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3396408654541646e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 28.**************}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.50", "creation_time": "13396278195596274", "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13396408654541646", "last_time_obsolete_http_credentials_removed": 1751804655.624512, "last_time_password_store_metrics_reported": 1751896301.345192, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_hash_data_list": [{"hash": "djEw55CRtZ3LEpszOE++1Yqx92GchEbQWeKwZHUDBDb34CGxwLzvi1fFhA==", "is_gaia": "djEwTC3szRQC9KHazz9V7tiR5O6c4dwCvHDAXh3mdalf334=", "last_signin": 1751870485.638798, "salt_length": "djEwClauhIQoTzr7abKdzNjV1D8YpwCom5I1i6imC4LvkJbCHH5HyDvay1VJSqcCBiA=", "username": "djEw0dXx4OtJ7CT2SHAe8HjlD8ZLXj05Yd/kMY61/8TJguuAS96ZmJFguObXLs0ZB2YrVJ8s"}], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13396537397754389", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13396364595", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "CuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/ELWEkN7W/eUXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEJ6GkN7W/eUXClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQv6Him4H75RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEOSh4puB++UX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13396319999000000", "uma_in_sql_start_time": "13396278195626829"}, "sessions": {"event_log": [{"crashed": false, "time": "13396278195625005", "type": 0}, {"crashed": true, "time": "13396278338031795", "type": 0}, {"crashed": true, "time": "13396344060898566", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "13396344274061800", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396344469120196", "type": 0}, {"crashed": true, "time": "13396353410649599", "type": 0}, {"crashed": true, "time": "13396363162438950", "type": 0}, {"crashed": true, "time": "13396369871343082", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"accounts_metadata_dict": {"106558212763703608723": {"BookmarksExplicitBrowserSigninEnabled": false, "ChromeSigninInterceptionDismissCount": 1, "ExtensionsExplicitBrowserSigninEnabled": false}}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true, "web_signin_accounts_start_time_dict": {"106558212763703608723": "*****************"}}, "spellcheck": {"dictionaries": ["en-GB"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "updateclientdata": {"apps": {"nmmhkkegccagdldgiimedpiccmgmieda": {"cohort": "1::", "cohortname": "", "dlrc": 6762, "installdate": 6761, "pf": "d3f5775d-470e-4f70-ac77-b8d9abb518fb"}}}, "web_apps": {"daily_metrics": {"https://www.seek.co.nz/": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 3, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "*****************", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[],[],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:suggesteventid\":\"-8821344697264719006\",\"google:suggesttype\":[],\"google:verbatimrelevance\":851}]"}}