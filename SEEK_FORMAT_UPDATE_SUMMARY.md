# Seek.co.nz Format Update - December 2024

## Overview
Seek.co.nz has updated their HTML structure again. The job listing elements now use different CSS classes. This document summarizes the changes made to adapt the bot to the new format.

## Key Changes Made

### 1. Job Container Selector
**Updated**: Main job listing container selector

**Before:**
```css
div.ube6hn0[data-search-sol-meta]
```

**After:**
```css
div._32fem00[data-search-sol-meta]
```

**Code Location**: Line 1800 in `seek_auto_apply.py`

### 2. Job Title Selector
**Status**: Already updated to use `data-automation` attribute

**Current Selector:**
```css
span[data-automation='jobTitle']
```

This is robust and should continue working across format changes.

### 3. Company Name Selector
**Status**: Already updated to use `data-automation` attribute

**Current Selector:**
```css
span[data-automation='jobAdvertiser']
```

This is robust and should continue working across format changes.

### 4. Location Selector
**Updated**: Added new CSS classes for location detection

**New Primary Selector:**
```css
span._32fem00._1nh354w50.gyz43x0.gyz43x1.gyz43x1u.gyz43x6._1lwlriv4
```

**Fallback Selectors:**
1. `span.ube6hn0.wc8kxl4z.m81yar0.m81yar1.m81yar1u.m81yar6` (previous format)
2. `span.xhgj00.ciuj3f4z.eu0zaq0.eu0zaq1.eu0zaq1u.eu0zaq6` (older format)

### 5. Quick Apply Button Selector
**Updated**: Added new CSS classes for Quick Apply button detection

**New Primary Selector:**
```css
span._32fem00._1nh354w50.gyz43x0.gyz43x2.gyz43x1t.gyz43xa._1lwlriv4
```

**Fallback Selectors:**
1. `span.ube6hn0.wc8kxl63.wc8kxlp.wc8kxl5f.wc8kxl5b.wc8kxlgj.wc8kxlgv.wc8kxly.wc8kxl5.wc8kxlib.wc8kxl4.de1uac2.de1uac7.m81yar11.m81yar13.wc8kxl17.wc8kxl18` (previous format)
2. `span.ube6hn0.wc8kxl8v.wc8kxl7r.wc8kxlb3.wc8kxl9z.wc8kxl5v.wc8kxlp.wc8kxl5f.wc8kxl5b.wc8kxlgj.wc8kxlgv.wc8kxly.wc8kxl5.wc8kxlib.wc8kxl4` (intermediate format)
3. `span.xhgj00.ciuj3f63.ciuj3fp.ciuj3f5f.ciuj3f5b.ciuj3fgj.ciuj3fgv.ciuj3fy.ciuj3f5.ciuj3fib.ciuj3f4.hcoqhn2.hcoqhn7.eu0zaq11.eu0zaq13.ciuj3f17.ciuj3f18` (older format)

## New HTML Structure Analysis

### Job Container Structure:
```html
<div class="_32fem00" data-search-sol-meta="{...}">
  <div class="_32fem00 _1nh354w98 _1nh354w84 _1nh354wbg _1nh354wac _1nh354w5g w39xvk6 w39xvk8 w39xvkc w39xvk7" aria-label="Job Title">
    <a href="/job/12345?ref=..." class="_32fem00 _32fem0g _32fem08 ..." data-automation="recommendedJobLink_12345">
      <!-- Job content -->
    </a>
    <!-- Job details -->
  </div>
</div>
```

### Key Elements:
- **Job Title**: `<span data-automation="jobTitle">Title</span>`
- **Company**: `<span data-automation="jobAdvertiser">Company</span>`
- **Location**: `<span class="_32fem00 _1nh354w50 gyz43x0 gyz43x1 gyz43x1u gyz43x6 _1lwlriv4">Location</span>`
- **Quick Apply**: `<span class="_32fem00 _1nh354w50 gyz43x0 gyz43x2 gyz43x1t gyz43xa _1lwlriv4">Quick apply</span>`

## Robustness Strategy

### 1. Multiple Fallback Selectors
Each critical element has multiple fallback selectors to handle:
- Current format (newest)
- Previous format
- Older formats

### 2. Data-Automation Attributes
Where possible, we use `data-automation` attributes which are more stable:
- `data-automation="jobTitle"`
- `data-automation="jobAdvertiser"`
- `data-automation="recommendedJobLink_*"`

### 3. Broad Search Fallbacks
For Quick Apply buttons, we also have a broad search that looks for any element containing "quick apply" text.

## Testing Recommendations

1. **Test job listing extraction** to ensure all jobs are found
2. **Test job title/company extraction** to verify data accuracy
3. **Test Quick Apply button detection** to ensure applications work
4. **Monitor for new format changes** and update selectors as needed

## Future Considerations

### Potential Improvements:
1. **Dynamic selector detection** - Automatically detect new CSS patterns
2. **Machine learning approach** - Train model to identify job elements
3. **API integration** - Use official Seek API if available
4. **Regular expression patterns** - Use text patterns as backup

### Monitoring:
- Set up alerts for failed job extractions
- Log selector success rates
- Monitor for new HTML patterns

## Files Modified
- `seek_auto_apply.py` - Updated selectors for new format
- `SEEK_FORMAT_UPDATE_SUMMARY.md` - This documentation

## Backward Compatibility
All changes maintain backward compatibility with previous formats through fallback selectors. The bot should continue working even if Seek reverts to older formats.
